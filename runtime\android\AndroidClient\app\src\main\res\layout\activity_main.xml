<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".MainActivity">

    <Button
        android:id="@+id/record_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="10dp"
        android:text="按下录音" />

    <com.yeyupiaoling.androidclient.AudioView
        android:id="@+id/audioView"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:layout_above="@id/record_button"
        android:layout_marginStart="10dp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/result_text"
        android:layout_above="@id/record_button"
        android:layout_width="match_parent"
        android:hint="显示识别结果"
        android:textSize="22sp"
        android:layout_height="match_parent"/>

</RelativeLayout>