<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="KaldiNativeFbankSharp" Version="1.1.2" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Microsoft.ML.OnnxRuntime" Version="1.15.0" />
    <PackageReference Include="YamlDotNet" Version="13.1.0" />
  </ItemGroup>

  <ItemGroup>
    <None Update="speech_fsmn_vad_zh-cn-16k-common-pytorch\example\0.wav">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="speech_fsmn_vad_zh-cn-16k-common-pytorch\example\1.wav">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="speech_fsmn_vad_zh-cn-16k-common-pytorch\model.onnx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="speech_fsmn_vad_zh-cn-16k-common-pytorch\vad.mvn">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="speech_fsmn_vad_zh-cn-16k-common-pytorch\vad.yaml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
