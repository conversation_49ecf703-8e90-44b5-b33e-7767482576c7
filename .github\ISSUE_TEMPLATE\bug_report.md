---
name: 🐛 Bug Report
about: Submit a bug report to help us improve
labels: 'bug, needs triage'
---

Notice: In order to resolve issues more efficiently, please raise issue following the template.
（注意：为了更加高效率解决您遇到的问题，请按照模板提问，补充细节）

## 🐛 Bug

<!-- A clear and concise description of what the bug is. -->

### To Reproduce

Steps to reproduce the behavior (**always include the command you ran**):

1. Run cmd '....'
2. See error

<!-- If you have a code sample, error messages, stack traces, please provide it here as well -->


#### Code sample
<!-- Ideally attach a minimal code sample to reproduce the decried issue.
Minimal means having the shortest code but still preserving the bug. -->

### Expected behavior

<!-- A clear and concise description of what you expected to happen. -->

### Environment

 - OS (e.g., Linux):
 - FunASR Version (e.g., 1.0.0):
 - ModelScope Version (e.g., 1.11.0):
 - PyTorch Version (e.g., 2.0.0):
 - How you installed funasr (`pip`, source):
 - Python version:
 - GPU (e.g., V100M32)
 - CUDA/cuDNN version (e.g., cuda11.7):
 - Docker version (e.g., funasr-runtime-sdk-cpu-0.4.1)
 - Any other relevant information:

### Additional context

<!-- Add any other context about the problem here. -->